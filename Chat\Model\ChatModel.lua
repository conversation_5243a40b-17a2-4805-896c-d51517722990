
ChatModel = {}

ChatModel.redis_index = "redis_chat"

ChatModel.chat_list = "chat_list_"   --每个订单后面，会创建一个聊天消息。如果这个聊天休息不更新，那么定时时间内，就会解散

ChatModel.chat_to_update = "chat_to_update_"  --需要同步到码商那边的列表


ChatModel.online_list = "chat_online_list"

ChatModel.state_to_update = "state_to_update_"  --需要同步到码商那边的列表

ChatModel.pay_to_update = "pay_to_update_"      --用户在H5端，




function ChatModel.InsertInto(otcorderid,pcorderid,vendor_userid,customer_user_id,deal_type,channel,chattype,msgtype,msgdata)

	--返回聊天的数据
	
	local timesec = TimeUtils.GetTime()
	local sqlCase = "insert into dy_chat_data(otcorderid,pcorderid,vendor_userid,customer_user_id,deal_type,channel,timesec, chattype,msgtype,msg) values('"..otcorderid.."','"..pcorderid.."',"..vendor_userid..","..customer_user_id..","..
		deal_type..",'"..channel.."',"..timesec..","..chattype..","..msgtype..",'"..msgdata.."')"

	mysqlItem:execute(sqlCase)
	
	local cInfo = st_chat_pb.chatdata()
	cInfo.timesec = timesec
	cInfo.chattype = chattype
	cInfo.msgtype = tonumber(msgtype)
	cInfo.msg = msgdata
	cInfo.pcorderid = pcorderid

	return cInfo
	
end

function ChatModel.Login(pcorderid, userid)
	
	redisItem:hset( ChatModel.online_list, pcorderid, userid, ChatModel.redis_index )
	
end

function ChatModel.Logout(pcorderid)
	redisItem:hdel( ChatModel.online_list, pcorderid, ChatModel.redis_index )
end

function ChatModel.GetUserID(pcorderid)
	return redisItem:hget( ChatModel.online_list, pcorderid, ChatModel.redis_index )
end

function ChatModel.Exist(pcorderid)
	return redisItem:hexists( ChatModel.online_list, pcorderid, ChatModel.redis_index )
end

function ChatModel.PushChatToUpdate(stype, cInfo)
	print("PushChatToUpdate======"..stype)
	redisItem:rpush( ChatModel.chat_to_update..stype, cInfo:SerializeToString(), ChatModel.redis_index)
end

function ChatModel.ChatToUpdateLen(stype)
	return redisItem:llen( ChatModel.chat_to_update..stype,  ChatModel.redis_index)
end

function ChatModel.PushStateToUpdate(stype,cInfo)
	redisItem:rpush( ChatModel.state_to_update..stype, cInfo:SerializeToString(), ChatModel.redis_index)
end


function ChatModel.StateToUpdateLen(stype)
	return redisItem:llen( ChatModel.state_to_update..stype,  ChatModel.redis_index)
end

function ChatModel.PushPayToUpdate(stype, strInfo)  --这边点击，我已经支付
	
	redisItem:rpush( ChatModel.pay_to_update..stype, strInfo, ChatModel.redis_index)
end

function ChatModel.PayToUpdateLen(stype)
	return redisItem:llen( ChatModel.pay_to_update..stype, ChatModel.redis_index )
end


