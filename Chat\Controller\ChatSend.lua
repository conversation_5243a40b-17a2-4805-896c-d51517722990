module("ChatSend", package.seeall)

--调用对应的接口，向结点端上报对应的信息
function execute(packetID, operateID, buffer)

	--AddressPreModel.DelGetWorker()
	--
	local cgmsg = msg_chat_pb.cgchatsend()
	local gcmsg = msg_chat_pb.gcchatsend()
	
	--cgmsg:ParseFromString(buffer)
	cgmsg:ParseFromString(buffer)
	--取到这个订单的消息，这个订单消息是在三方平台的数据库获取
	
	if cgmsg.channel == '' or cgmsg.pcorderid=='' or cgmsg.chatid==0 then
		
		gcmsg.result, gcmsg.msg = -1,"发送失败"
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	
	
	local sqlCase = "select id,merchant_order_id,vendor_user_id,customer_user_id,type,channel from dy_customer_order where id>0 and merchant_order_id='"..cgmsg.pcorderid.."'"
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		
		gcmsg.result, gcmsg.msg = -2,"发送失败"
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
		
	end	
	
	local otc_orderid = sqlData[1]
	local vendor_user_id = sqlData[3]
	local customer_user_id = sqlData[4]
	local merchant_order_id = sqlData[2]
	local chattype = g_chatDefine.chat_type['customer_to_merchant']

	
	local getData = ChatModel.InsertInto(sqlData[1],sqlData[2],sqlData[3],sqlData[4],sqlData[5],sqlData[6],chattype,cgmsg.msgtype,cgmsg.msg)
	
	if getData ~= nil then
		
		
		gcmsg.cdata.timesec = getData.timesec
		gcmsg.cdata.chattype = getData.chattype
		gcmsg.cdata.msgtype = getData.msgtype

		gcmsg.cdata.msg = getData.msg
		gcmsg.cdata.pcorderid = cgmsg.pcorderid
		--gcmsg.pcorderid = cgmsg.pcorderid    --对于币商的用户，这里的订单号，是平台的订单号的。
		
		--这这里需要，需要同步到币商那边。
		gcmsg.result, gcmsg.msg = 0,"发送成功"

				
		local gcrcv = msg_chat_pb.gcchatrecv()
		
		gcrcv.cdata.timesec = getData.timesec
		gcrcv.cdata.chattype = getData.chattype
		gcrcv.cdata.msgtype = getData.msgtype

		gcrcv.cdata.msg = getData.msg
		gcrcv.cdata.pcorderid = otc_orderid
		gcrcv.pcorderid = otc_orderid 
		gcrcv.target = vendor_user_id
		gcrcv.result, gcrcv.msg = 0,"发送成功"
		
		ChatModel.PushChatToUpdate("merchant",gcrcv)    --推送到核心服务器
	else
		gcmsg.result, gcmsg.msg = 1,"发送失败"
	end
	
	
	return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
end