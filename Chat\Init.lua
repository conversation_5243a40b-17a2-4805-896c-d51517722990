
PacketCode = {}
require("common.packet.packet_chat")
require("common.st_chat_pb")
require("common.msg_chat_pb")
require("common.define.ChatDefine")


require("Chat.Controller.ChatCreate")

require("Chat.Controller.ChatSend")

require("Chat.Controller.Heartbeat")
require("Chat.Controller.ReConnect")
require("Chat.Controller.ChatPay")
require("Chat.Worker.ChatUpdatePop")

require("Chat.Model.ChatModel")

require("Chat.Services.ChatServices")


g_redisIndex[ChatModel.redis_index] = {index = g_redisInfo.redis_two, key = ChatModel.redis_index, link = 1} 



