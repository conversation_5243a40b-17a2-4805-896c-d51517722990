
--该类是用于处理C++传过来多线程的问题，
--包括共享内存锁的机制
--对于线程的锁，已经要记住，lock的时候一定要unlock
ThreadManager = {}
ThreadManager.redis_index = "redis_thread"

function ThreadManager.OrderLock(index)
	local redisKey = "lock_order_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.OrderUnLock(index)
	local redisKey = "lock_order_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end


function ThreadManager.DealLock(index)
	local redisKey = "lock_deal_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisK<PERSON>, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.DealUnLock(index)
	local redisKey = "lock_deal_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end