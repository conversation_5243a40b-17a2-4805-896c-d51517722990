RedisClearModel = {}
RedisClearModel.redis_index = "redis_redisclear"
RedisClearModel.normal_key = "redisclear_normal_key"
RedisClearModel.hash_field_key = "redisclear_hash_field_key"



-- 要求key值中不存，符号 "#"
-- 栗子：RedisClearModel.AddNormalKey("testkey", 30, CpBrnnModel.redis_index)
function RedisClearModel.AddNormalKey(key, expiretime, redisindex)
	local start_,end_ = string.find(key..redisindex, "#")
	
	if start_ ~= nil then
		return false
	end
	
	local combinekey = key.."#"..redisindex
	local time = TimeUtils.GetTime() + expiretime
	redisItem:zadd(RedisClearModel.normal_key, time, combinekey, RedisClearModel.redis_index)
	
	return true
end


-- 要求key值或者域中不存，符号 "#"
-- fiel：域值
-- expiretime：域值的过期时间
-- 栗子：RedisClearModel.AddHashFieldKey("testhashkey", "1", 30, CpBrnnModel.redis_index)
function RedisClearModel.AddHashFieldKey(key, fiel, expiretime, redisindex)
	local start_,end_ = string.find(key..fiel..redisindex, "#")
	
	if start_ ~= nil then
		return false
	end
	
	local combinekey = key.."#"..fiel.."#"..redisindex
	local time = TimeUtils.GetTime() + expiretime
	redisItem:zadd(RedisClearModel.hash_field_key, time, combinekey, RedisClearModel.redis_index)
	
	return true
end