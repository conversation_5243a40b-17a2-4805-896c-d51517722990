# OTC聊天服务器Lua环境测试脚本
Write-Host "=== OTC聊天服务器Lua环境测试 ===" -ForegroundColor Green

# 检查Lua环境
Write-Host "`n检查Lua环境..." -ForegroundColor Yellow

$luaFound = $false
$luaPath = ""

# 检查本地lua51目录
if (Test-Path "lua51\lua.exe") {
    Write-Host "发现本地Lua: lua51\lua.exe" -ForegroundColor Green
    $luaPath = "lua51\lua.exe"
    $luaFound = $true
    & $luaPath -v
}

# 检查系统PATH中的lua
if (!$luaFound) {
    try {
        $version = lua -v 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "发现系统Lua:" -ForegroundColor Green
            Write-Host $version -ForegroundColor Cyan
            $luaPath = "lua"
            $luaFound = $true
        }
    } catch {
        # Lua不在PATH中
    }
}

if (!$luaFound) {
    Write-Host "Lua未安装，请先安装Lua 5.1" -ForegroundColor Red
    Write-Host "`n安装方法:" -ForegroundColor Yellow
    Write-Host "1. 运行: PowerShell -ExecutionPolicy Bypass -File get_lua.ps1" -ForegroundColor Cyan
    Write-Host "2. 或查看: LUA_INSTALL_GUIDE.txt" -ForegroundColor Cyan
    return
}

# 检查项目文件
Write-Host "`n检查项目文件结构..." -ForegroundColor Yellow

$files = @(
    "lioGame.exe",
    "executor.lua", 
    "LoadHelper.lua",
    "conf\serverConf.lua",
    "lua_c_functions.lua",
    "start_lua_server.lua"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "[OK] $file" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] $file 不存在" -ForegroundColor Red
    }
}

# 尝试运行纯Lua版本
Write-Host "`n尝试运行纯Lua版本..." -ForegroundColor Yellow

if ((Test-Path "start_lua_server.lua") -and (Test-Path "lua_c_functions.lua")) {
    Write-Host "启动纯Lua服务器..." -ForegroundColor Cyan
    
    try {
        & $luaPath start_lua_server.lua
        Write-Host "`n✓ 纯Lua服务器运行成功！" -ForegroundColor Green
    } catch {
        Write-Host "`n✗ 纯Lua服务器运行失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "[ERROR] 缺少必要的Lua文件" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果看到服务器启动信息，说明纯Lua环境搭建成功！" -ForegroundColor Yellow
Write-Host "项目可以脱离C++框架运行。" -ForegroundColor Yellow
