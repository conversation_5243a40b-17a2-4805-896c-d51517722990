# 简单的项目检查脚本
Write-Host "=== OTC聊天服务器项目检查 ===" -ForegroundColor Green

# 检查关键文件
Write-Host "`n检查关键文件:" -ForegroundColor Yellow
$files = @(
    "lioGame.exe",
    "executor.lua", 
    "LoadHelper.lua",
    "conf\serverConf.lua"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $file" -ForegroundColor Red
    }
}

# 检查可执行文件信息
if (Test-Path "lioGame.exe") {
    Write-Host "`n可执行文件信息:" -ForegroundColor Yellow
    $info = Get-ItemProperty "lioGame.exe"
    Write-Host "  大小: $($info.Length) 字节" -ForegroundColor Cyan
    Write-Host "  时间: $($info.LastWriteTime)" -ForegroundColor Cyan
}

# 检查配置文件
if (Test-Path "conf\serverConf.lua") {
    Write-Host "`n配置文件检查:" -ForegroundColor Yellow
    $content = Get-Content "conf\serverConf.lua" -Raw
    if ($content -match 'g_serverip\s*=\s*"([^"]+)"') {
        Write-Host "  服务器IP: $($matches[1])" -ForegroundColor Cyan
    }
    if ($content -match 'g_tcpport\s*=\s*(\d+)') {
        Write-Host "  TCP端口: $($matches[1])" -ForegroundColor Cyan
    }
    if ($content -match 'g_httpport\s*=\s*(\d+)') {
        Write-Host "  HTTP端口: $($matches[1])" -ForegroundColor Cyan
    }
}

Write-Host "`n=== 检查完成 ===" -ForegroundColor Green
