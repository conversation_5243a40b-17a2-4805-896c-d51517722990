module("ChatCreate", package.seeall)

--
function execute(packetID, operateID, buffer)
	
	--创建的时候
	--AddressPreModel.DelGetWorker()
	local cgmsg = msg_chat_pb.cgchatcrate()
	local gcmsg = msg_chat_pb.gcchatcrate()
	
	--首先去三方支付平台，查看该订单存不存在
	cgmsg:ParseFromString(buffer)

	if cgmsg.channel == '' or cgmsg.pcorderid=='' then
		
		gcmsg.result, gcmsg.msg = -1,"参数类型错误"
		return cgmsg.userid,-1,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end


	
	local sqlCase = "select channel,price,money,status,pay_id,user_pay_id,amount,id,payee_account,payee_name,payee_bank,payee_band_addr,create_time,customer_user_id from dy_customer_order where id>0 and merchant_order_id='"..cgmsg.pcorderid.."'"

	mysqlItem:executeQuery(sqlCase)
	--print(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		
		gcmsg.result, gcmsg.msg = -1,"创建聊天室失败"
		return cgmsg.pcorderid,-1,gcmsg:ByteSize(),gcmsg:SerializeToString()
		
	end
	gcmsg.result = 0

	gcmsg.channel = sqlData[1]
	gcmsg.coinprice = sqlData[2]
	gcmsg.money = sqlData[3]
	gcmsg.state = tonumber(sqlData[4])
	gcmsg.timeout = g_chatDefine.time_out['start_pay']  

	gcmsg.payid = sqlData[5]
	
	
	local user_pay_id = sqlData[6]
	gcmsg.coinamount = sqlData[7]
	

	gcmsg.payaccount = sqlData[9]
	gcmsg.payusername = sqlData[10]
	gcmsg.paybankname = sqlData[11]
	gcmsg.paybankaddress = sqlData[12]
	--gcmsg.payurl = sqlData[21]

	if tonumber(sqlData[4]) == 1 then
		--只有未付款的才会有
		local timeMark = TimeUtils.GetTime() - TimeUtils.GetTime( sqlData[13] )
		gcmsg.timeout = 900 - timeMark 
		gcmsg.timeout = gcmsg.timeout < 0 and 0 or gcmsg.timeout
	else
		gcmsg.timeout = 0
	end

	
	sqlCase = "select is_up_payername,is_up_payvoucher from dy_user_info where userid="..sqlData[14]
	mysqlItem:executeQuery(sqlCase)
	sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		gcmsg.ispayname = sqlData[1]
		gcmsg.ispayvoucher = sqlData[2]
	else
		gcmsg.ispayname = '0'
		gcmsg.ispayvoucher = '0'	
	end



	
	--这里需要防止出现多个。
	sqlCase = "select timesec,chattype,msgtype,msg from dy_chat_data where pcorderid='"..cgmsg.pcorderid.."'"

	mysqlItem:executeQuery(sqlCase)
	local itmeCount = 0
	for i = 1,100 do
		sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local addItem = gcmsg.chatlist:add()
		addItem.timesec = tonumber(sqlData[1])
		addItem.chattype = tonumber(sqlData[2])
		addItem.msgtype = tonumber(sqlData[3])
		addItem.msg = sqlData[4]
		addItem.pcorderid = cgmsg.pcorderid
		
	end
	
	
	
	--以订单号为准	

	--这里随机匹配一个userid。
	
	local userID = math.myrandom(100000000, 900000000)

	ChatModel.Login(cgmsg.pcorderid, userID)
	
	return userID,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
end