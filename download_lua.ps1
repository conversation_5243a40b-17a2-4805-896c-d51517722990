# PowerShell脚本：下载并安装Lua 5.1
param(
    [string]$InstallPath = "C:\lua51"
)

Write-Host "=== Lua 5.1 安装脚本 ===" -ForegroundColor Green

# 创建安装目录
if (!(Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force
    Write-Host "创建目录: $InstallPath" -ForegroundColor Yellow
}

# 尝试下载Lua 5.1二进制文件
$urls = @(
    "https://sourceforge.net/projects/luabinaries/files/5.1.5/Tools%20Executables/lua-5.1.5_Win64_bin.zip/download",
    "https://github.com/rjpcomputing/luaforwindows/releases/download/v5.1.5-52/LuaForWindows_v5.1.5-52.exe"
)

$downloaded = $false

foreach ($url in $urls) {
    try {
        Write-Host "尝试从 $url 下载..." -ForegroundColor Yellow
        
        if ($url.EndsWith(".zip")) {
            $zipFile = Join-Path $env:TEMP "lua51.zip"
            Invoke-WebRequest -Uri $url -OutFile $zipFile -TimeoutSec 30
            
            # 解压文件
            Expand-Archive -Path $zipFile -DestinationPath $InstallPath -Force
            Remove-Item $zipFile
            
            Write-Host "Lua 5.1 下载并解压完成" -ForegroundColor Green
            $downloaded = $true
            break
        }
    }
    catch {
        Write-Host "下载失败: $($_.Exception.Message)" -ForegroundColor Red
        continue
    }
}

if (!$downloaded) {
    Write-Host "自动下载失败，请手动下载Lua 5.1" -ForegroundColor Red
    Write-Host "1. 访问: https://luabinaries.sourceforge.net/download.html" -ForegroundColor Yellow
    Write-Host "2. 下载: lua-5.1.5_Win64_bin.zip" -ForegroundColor Yellow
    Write-Host "3. 解压到: $InstallPath" -ForegroundColor Yellow
    
    # 创建一个简单的Lua测试文件
    $testLua = @"
print("Lua 5.1 测试")
print("版本: " .. _VERSION)
print("当前目录: " .. (io.popen("cd"):read("*l") or "未知"))
"@
    
    $testFile = Join-Path $InstallPath "test.lua"
    $testLua | Out-File -FilePath $testFile -Encoding UTF8
    
    Write-Host "创建了测试文件: $testFile" -ForegroundColor Green
}

# 检查PATH环境变量
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$InstallPath*") {
    Write-Host "需要将 $InstallPath 添加到PATH环境变量" -ForegroundColor Yellow
    Write-Host "请手动执行以下命令或重启PowerShell:" -ForegroundColor Yellow
    Write-Host "`$env:PATH += ';$InstallPath'" -ForegroundColor Cyan
}

Write-Host "=== 安装完成 ===" -ForegroundColor Green
Write-Host "测试命令: lua -v" -ForegroundColor Cyan
