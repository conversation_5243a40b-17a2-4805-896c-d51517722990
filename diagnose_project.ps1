# PowerShell脚本：诊断OTC聊天服务器项目
Write-Host "=== OTC聊天服务器项目诊断 ===" -ForegroundColor Green

# 检查项目文件结构
Write-Host "`n1. 检查项目文件结构..." -ForegroundColor Yellow

$requiredFiles = @(
    "lioGame.exe",
    "executor.lua", 
    "LoadHelper.lua",
    "startwin.bat",
    "conf\serverConf.lua",
    "conf\redisConf.lua",
    "trdlib\Init.lua",
    "Chat\Init.lua",
    "common\proto\msg_chat.proto"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $file" -ForegroundColor Red
        $missingFiles += $file
    }
}

# 检查可执行文件
Write-Host "`n2. 检查可执行文件..." -ForegroundColor Yellow
if (Test-Path "lioGame.exe") {
    $fileInfo = Get-ItemProperty "lioGame.exe"
    Write-Host "  文件大小: $($fileInfo.Length) 字节" -ForegroundColor Cyan
    Write-Host "  修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
    
    # 尝试运行可执行文件
    Write-Host "  尝试运行可执行文件..." -ForegroundColor Cyan
    try {
        $process = Start-Process -FilePath ".\lioGame.exe" -ArgumentList "utils" -PassThru -WindowStyle Hidden
        Start-Sleep -Seconds 2
        if (!$process.HasExited) {
            Write-Host "  ✓ 可执行文件可以启动" -ForegroundColor Green
            $process.Kill()
        } else {
            Write-Host "  ✗ 可执行文件立即退出 (退出代码: $($process.ExitCode))" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  ✗ 无法启动可执行文件: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 检查配置文件语法
Write-Host "`n3. 检查配置文件语法..." -ForegroundColor Yellow

$configFiles = @("conf\serverConf.lua", "conf\redisConf.lua")
foreach ($configFile in $configFiles) {
    if (Test-Path $configFile) {
        Write-Host "  检查 $configFile..." -ForegroundColor Cyan
        $content = Get-Content $configFile -Raw
        
        # 简单的语法检查
        $syntaxErrors = @()
        
        # 检查引号匹配
        $quotes = ($content | Select-String '"' -AllMatches).Matches.Count
        if ($quotes % 2 -ne 0) {
            $syntaxErrors += "引号不匹配"
        }
        
        # 检查常见错误
        if ($content -match '""[^"]') {
            $syntaxErrors += "发现双引号错误"
        }
        
        if ($syntaxErrors.Count -eq 0) {
            Write-Host "    ✓ 语法检查通过" -ForegroundColor Green
        } else {
            Write-Host "    ✗ 发现语法错误: $($syntaxErrors -join ', ')" -ForegroundColor Red
        }
    }
}

# 检查端口占用
Write-Host "`n4. 检查端口占用..." -ForegroundColor Yellow
$ports = @(6011, 6012, 6013, 6379, 3306)
foreach ($port in $ports) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "  端口 $port : 已占用" -ForegroundColor Yellow
        } else {
            Write-Host "  端口 $port : 可用" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  端口 $port : 无法检测" -ForegroundColor Gray
    }
}

# 检查依赖服务
Write-Host "`n5. 检查依赖服务..." -ForegroundColor Yellow

# 检查MySQL
try {
    $mysqlService = Get-Service -Name "*mysql*" -ErrorAction SilentlyContinue
    if ($mysqlService) {
        Write-Host "  MySQL服务: $($mysqlService.Status)" -ForegroundColor Cyan
    } else {
        Write-Host "  MySQL服务: 未安装" -ForegroundColor Red
    }
}
catch {
    Write-Host "  MySQL服务: 检查失败" -ForegroundColor Red
}

# 检查Redis
$redisProcesses = Get-Process -Name "*redis*" -ErrorAction SilentlyContinue
if ($redisProcesses) {
    Write-Host "  Redis进程: 运行中 ($($redisProcesses.Count) 个进程)" -ForegroundColor Green
} else {
    Write-Host "  Redis进程: 未运行" -ForegroundColor Red
}

# 生成诊断报告
Write-Host "`n=== 诊断报告 ===" -ForegroundColor Green

if ($missingFiles.Count -gt 0) {
    Write-Host "缺失文件:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
}

Write-Host "`n建议的下一步操作:" -ForegroundColor Yellow
Write-Host "1. 安装Lua 5.1环境" -ForegroundColor Cyan
Write-Host "2. 安装MySQL数据库" -ForegroundColor Cyan  
Write-Host "3. 安装Redis缓存" -ForegroundColor Cyan
Write-Host "4. 检查Visual C++ Redistributable" -ForegroundColor Cyan
Write-Host "5. 运行 lua test_lua_env.lua 测试环境" -ForegroundColor Cyan

Write-Host "`n=== 诊断完成 ===" -ForegroundColor Green
