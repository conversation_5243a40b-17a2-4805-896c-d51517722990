# OTC聊天服务器开发环境搭建指南

## 项目概述

这是一个基于C++和Lua的聊天服务器项目，主要用于OTC（场外交易）系统的实时聊天功能。

### 技术栈
- **后端引擎**: C++ (lioGame.exe/run_chatserver)
- **业务逻辑**: Lua 5.1
- **协议**: Protocol Buffers
- **数据库**: MySQL + Redis
- **网络协议**: TCP, HTTP, WebSocket

### 项目结构
```
chatserver/
├── lioGame.exe          # Windows服务器可执行文件
├── run_chatserver       # Linux服务器可执行文件
├── executor.lua         # Lua主执行脚本
├── LoadHelper.lua       # 模块加载器
├── startwin.bat         # Windows启动脚本
├── conf/                # 配置文件目录
│   ├── serverConf.lua   # 服务器配置
│   └── redisConf.lua    # Redis配置
├── trdlib/              # 第三方库
│   ├── Init.lua         # 库初始化
│   ├── external/        # 外部库(JSON, MySQL, Redis等)
│   ├── libprotobuf/     # Protobuf库
│   └── lua/             # Lua扩展
├── Chat/                # 聊天模块
├── common/              # 公共模块
│   ├── proto/           # Protobuf定义文件
│   └── packet/          # 数据包定义
└── log/                 # 日志目录
```

## 环境要求

### 系统要求
- Windows 10/11 或 Linux
- 至少2GB内存
- 网络连接

### 软件依赖
1. **Lua 5.1** - 脚本解释器
2. **MySQL 5.7+** - 主数据库
3. **Redis 6.0+** - 缓存数据库
4. **Visual C++ Redistributable** (Windows) - 运行时库

## 安装步骤

### 1. 安装Lua 5.1

#### Windows方式1: 下载预编译版本
1. 访问 https://luabinaries.sourceforge.net/download.html
2. 下载 `lua-5.1.5_Win64_bin.zip`
3. 解压到 `C:\lua51\`
4. 将 `C:\lua51\` 添加到系统PATH环境变量

#### Windows方式2: 使用包管理器
```powershell
# 使用Chocolatey
choco install lua

# 或使用Scoop
scoop install lua
```

#### 验证安装
```bash
lua -v
# 应该显示: Lua 5.1.x
```

### 2. 安装MySQL

#### Windows
1. 下载MySQL Community Server 8.0
2. 安装时设置root密码为 `root` (或修改配置文件)
3. 创建数据库:
```sql
CREATE DATABASE otc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### 配置
修改 `conf/serverConf.lua` 中的数据库配置:
```lua
g_dbHost = '127.0.0.1'
g_dbUser = 'root'
g_dbPassword = "root"
g_dbPort = 3306
g_dbDatabase = 'otc'
```

### 3. 安装Redis

#### Windows
1. 下载Redis for Windows
2. 解压并运行 `redis-server.exe`
3. 默认端口6379，无密码

#### 配置
Redis配置在 `conf/serverConf.lua`:
```lua
g_redisInfo.host = "127.0.0.1"
g_redisInfo.port = 6379
g_redisInfo.pass = ""
```

### 4. 安装Visual C++ Redistributable (Windows)
下载并安装最新的 Microsoft Visual C++ Redistributable

## 配置修复

项目中发现的配置错误已修复:
- `conf/serverConf.lua` 第70行语法错误已修复

## 测试环境

运行测试脚本验证环境:
```bash
lua test_lua_env.lua
```

## 启动服务器

### Windows
```cmd
# 方式1: 使用批处理文件
startwin.bat

# 方式2: 直接运行
lioGame.exe utils
```

### Linux
```bash
./run_chatserver utils
```

## 端口配置

默认端口配置 (可在serverConf.lua中修改):
- HTTP: 6012
- TCP: 6011  
- WebSocket: 6013
- UDP: 6004 (暂不支持)

## 常见问题

### 1. Lua模块加载失败
- 检查package.path是否正确
- 确保所有.lua文件存在且语法正确

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库配置信息
- 确保数据库存在

### 3. Redis连接失败
- 检查Redis服务是否启动
- 验证Redis配置信息

### 4. 可执行文件无法运行
- 检查Visual C++ Redistributable是否安装
- 确保所有DLL文件存在

## 开发建议

1. 使用支持Lua语法高亮的编辑器 (VS Code + Lua插件)
2. 定期备份数据库
3. 查看log目录下的日志文件排查问题
4. 修改配置后需要重启服务器

## 项目分析结果

### 当前状态
✅ **项目文件完整**: 所有核心文件都存在
✅ **配置文件已修复**: serverConf.lua语法错误已修复
✅ **可执行文件存在**: lioGame.exe (658KB, 2020年版本)
❌ **Lua环境缺失**: 需要安装Lua 5.1解释器
❌ **数据库未配置**: 需要安装MySQL和Redis

### 核心文件结构
```
✓ lioGame.exe          # C++服务器引擎 (658KB)
✓ executor.lua         # Lua主执行脚本
✓ LoadHelper.lua       # 模块加载器
✓ conf/serverConf.lua  # 服务器配置 (已修复语法错误)
✓ conf/redisConf.lua   # Redis配置
✓ trdlib/Init.lua      # 第三方库初始化
✓ Chat/Init.lua        # 聊天模块
✓ common/proto/        # Protobuf协议定义
```

### 技术特点分析
1. **混合架构**: C++引擎 + Lua业务逻辑
2. **协议支持**: TCP(6011), HTTP(6012), WebSocket(6013)
3. **数据存储**: MySQL(主数据) + Redis(缓存)
4. **消息格式**: Protocol Buffers序列化
5. **模块化设计**: Chat, LogServer, RedisClear等模块

### 业务功能
- 实时聊天 (WebSocket)
- 订单状态同步
- 支付状态通知
- 心跳保活
- 用户重连
- 日志记录

## 快速启动指南

### 最小环境搭建
1. **安装Lua 5.1**
   ```bash
   # 下载: https://luabinaries.sourceforge.net/
   # 解压到: C:\lua51\
   # 添加到PATH环境变量
   ```

2. **安装MySQL**
   ```sql
   CREATE DATABASE otc CHARACTER SET utf8mb4;
   ```

3. **安装Redis**
   ```bash
   # 下载Redis for Windows
   # 启动: redis-server.exe
   ```

4. **测试运行**
   ```cmd
   # 检查Lua环境
   lua test_lua_env.lua

   # 启动服务器
   lioGame.exe utils
   ```

## 开发建议

### 代码结构
- **Controller**: 处理网络请求 (Chat/Controller/)
- **Model**: 数据模型和数据库操作 (Chat/Model/)
- **Services**: 业务逻辑服务 (Chat/Services/)
- **Worker**: 后台任务处理 (Chat/Worker/)

### 调试技巧
1. 查看日志文件 (log/目录)
2. 修改g_isDebug=1启用调试模式
3. 使用LogFile()函数记录调试信息
4. 监控Redis和MySQL连接状态

### 扩展开发
1. 新增Controller处理新的协议
2. 在common/proto/添加新的protobuf定义
3. 使用Worker处理异步任务
4. 通过Redis实现分布式缓存

## 下一步

环境搭建完成后，可以:
1. 研究Chat模块的业务逻辑
2. 了解Protocol Buffers协议定义
3. 测试WebSocket连接
4. 开发新的聊天功能
5. 优化数据库查询性能
6. 添加新的支付方式支持
