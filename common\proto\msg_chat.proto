package prootc;

import "st_chat.proto";
//创建一个websocket链接
message cgchatcrate 
{
	optional string channel = 1;			 	//昵称
	optional string  pcorderid= 2;     			//支付中心paycenter的订单ID
	optional int32 userid = 3;                   // 在app聊天中，需要带上userid
}

message gcchatcrate
{
	optional int32 result = 1;
	optional string msg = 2;				//支付中心paycenter的订单ID
	optional string channel = 3;
	optional string pcorderid = 4;
	optional int32 state = 5;   //定单状态
	optional string money = 6;   //订单金额
	optional int32 timeout = 7;   //倒计时
	optional string payid = 8;
	
	optional string payaccount = 9;     //支付账户，
	optional string payusername = 10;   //支付姓名
	optional string paybankname = 12;   //如果是银行卡知道，就返回银行名字和开户地址
	optional string paybankaddress = 13;
	optional string payurl = 14;    //是扫码的，那么就是对应的二维码链接
	
	repeated chatdata chatlist = 15;   
	optional string coinprice = 16;
	optional string coinamount = 17;
	optional string ordertime = 18;        //订单时间
	optional string ispayname = 19;    //付款人毕传
	optional string ispayvoucher = 20;  //凭证毕传
}


//超时设置，这个是很久没有更新，自动超时
message cgchattimeout
{
	optional string channel = 1;			 	//昵称
	optional string  pcorderid= 2;     			//三方平台的订单ID	
}

//
message gcchattimeout
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语
}

message cgchatsend    //客服端发送对应的协议
{
	optional string channel = 1;			 	//昵称
	optional string  pcorderid= 2;     			//三方平台的订单ID
	optional int32 userid = 3;                 //
	optional string msgtype = 4;//消息类型：0:默认数字聊天，1：文字，2：图片，3：链接
	optional string msg = 5;     //如果消息类型是链接的话，那么就传入一个链接
}

message gcchatsend
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语	
	optional chatdata cdata = 3;                 //收到的聊天消息
	
}

message gcchatrecv   //客户端收到对应的协议
{
	optional int32 result = 1;			 	//
	optional string  pcorderid= 2;     			//三方平台的订单ID
	optional chatdata cdata = 3;                 //收到的聊天消息,在app聊天中，这个是订单ID
	optional string msg = 4;				//当result 不等0时 这里赋值有提示语	
	optional string target = 5;           //当用户给这个去买币时， 那么就需要添加去给推送给币商
}

//心跳包
message cgheartbeat
{
	optional int32 pcorderid = 1;
}
message gcheartbeat
{
	optional int32 result = 1;
	optional string msg = 2;
}

//重连
message cgreconnect
{
	optional int32 pcorderid = 1;
}
message gcreconnect
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 systime = 4;   				//服务器时间戳，用于客户端校准
}

message gcchatupdate //更新订单的状态
{
	optional int32 result = 1;
	optional string msg = 2;
	optional string  pcorderid= 3;     			//三方平台的订单ID
	optional int32 state = 4;                  //客户
	optional string target = 5;
}

message cgchatpay            //用户点击我已经支付，需要修改订单状态
{
	optional string channel = 1;			 	//昵称
	optional string  pcorderid= 2;     			//支付中心paycenter的订单ID
}

message gcchatpay    //点击完成后，需要发送消息到OTC服务器，所以这里
{
	optional int32 result = 1;
	optional string msg = 2;	
}

