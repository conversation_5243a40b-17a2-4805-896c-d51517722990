@echo off
chcp 65001 >nul
echo === OTC聊天服务器Lua环境测试 ===
echo.

REM 检查是否有lua.exe
if exist "lua51\lua.exe" (
    echo 发现本地Lua: lua51\lua.exe
    lua51\lua.exe -v
    echo.
    goto :test_project
)

REM 检查系统PATH中的lua
lua -v >nul 2>&1
if %errorlevel% == 0 (
    echo 发现系统Lua:
    lua -v
    echo.
    goto :test_project
)

echo Lua未安装，请先安装Lua 5.1
echo.
echo 安装方法:
echo 1. 运行: PowerShell -ExecutionPolicy Bypass -File get_lua.ps1
echo 2. 或查看: LUA_INSTALL_GUIDE.txt
echo.
pause
goto :end

:test_project
echo 测试项目文件结构...
echo.

REM 检查关键文件
set "files=lioGame.exe executor.lua LoadHelper.lua conf\serverConf.lua"
for %%f in (%files%) do (
    if exist "%%f" (
        echo [OK] %%f
    ) else (
        echo [ERROR] %%f 不存在
    )
)

echo.
echo 测试Lua C++函数替代实现...
if exist "lua_c_functions.lua" (
    echo [OK] lua_c_functions.lua 存在
) else (
    echo [ERROR] lua_c_functions.lua 不存在
)

echo.
echo 尝试运行纯Lua版本...
if exist "start_lua_server.lua" (
    if exist "lua51\lua.exe" (
        echo 使用本地Lua运行...
        lua51\lua.exe start_lua_server.lua
    ) else (
        echo 使用系统Lua运行...
        lua start_lua_server.lua
    )
) else (
    echo [ERROR] start_lua_server.lua 不存在
)

echo.
echo === 测试完成 ===
echo.
echo 如果看到服务器启动信息，说明纯Lua环境搭建成功！
echo 项目可以脱离C++框架运行。
echo.

:end
pause
