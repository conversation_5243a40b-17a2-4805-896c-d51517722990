module("ChatUpdatePop", package.seeall)

function work(buffer)

	
	
	local getLen = ChatModel.ChatToUpdateLen("player")

	
	for i = 1, getLen do

		local getData = redisItem:lpop( ChatModel.chat_to_update.."player", ChatModel.redis_index )
		if getData == nil then
			break
		end
		
		local gcmsg = msg_chat_pb.gcchatrecv()
		gcmsg:ParseFromString(getData)


		local userID = ChatModel.GetUserID(gcmsg.target)
		
		if userID ~= nil then

			gcmsg.result = 0
			SendMessage( userID, PacketCode[4012].client, gcmsg:ByteSize(),gcmsg:SerializeToString())
			
		end		
	end	
	
	
	getLen = ChatModel.StateToUpdateLen("player")

	
	for i = 1, getLen do
	
		local getData = redisItem:lpop( ChatModel.chat_to_update.."player", ChatModel.redis_index )
		if getData == nil then
			break
		end
		
		local gcmsg = msg_chat_pb.gcchatupdate()
		gcmsg.cdata:ParseFromString(getData)
		local userID = ChatModel.GetUserID(gcmsg.target)
		
		if userID ~= nil then
			
			gcmsg.result = 0
			SendMessage( userID, PacketCode[4003].client, gcmsg:ByteSize(),gcmsg:SerializeToString())
	
		end		
	end	
	


end

