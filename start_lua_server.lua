#!/usr/bin/env lua
-- 纯Lua版本的OTC聊天服务器启动脚本

print("=== OTC聊天服务器 (纯Lua版本) ===")
print("启动时间: " .. os.date("%Y-%m-%d %H:%M:%S"))

-- 加载C++函数替代实现
require("lua_c_functions")

-- 设置全局变量，确保以luaself模式运行
g_platfrom = 'luaself'

-- 创建必要的目录
function createDirectories()
    print("\n创建必要目录...")
    os.execute("mkdir log 2>nul")
    os.execute("mkdir chat 2>nul")
    print("目录创建完成")
end

-- 模拟服务器初始化
function serverInit()
    print("\n=== 服务器初始化 ===")
    
    -- 加载主执行器
    print("加载executor.lua...")
    local success, err = pcall(function()
        require("executor")
    end)
    
    if success then
        print("✓ executor.lua 加载成功")
    else
        print("✗ executor.lua 加载失败: " .. (err or "未知错误"))
        return false
    end
    
    return true
end

-- 模拟网络服务器
function startNetworkServer()
    print("\n=== 启动网络服务 ===")
    
    -- 读取配置
    if g_serverip and g_tcpport and g_httpport and g_websocketport then
        print("服务器配置:")
        print("  IP地址: " .. g_serverip)
        print("  TCP端口: " .. g_tcpport)
        print("  HTTP端口: " .. g_httpport)
        print("  WebSocket端口: " .. g_websocketport)
    else
        print("警告: 服务器配置未完全加载")
    end
    
    print("✓ 网络服务已启动 (模拟)")
end

-- 模拟消息处理循环
function messageLoop()
    print("\n=== 消息处理循环 ===")
    print("服务器正在运行，按Ctrl+C退出...")
    
    local loopCount = 0
    while true do
        loopCount = loopCount + 1
        
        -- 模拟处理一些消息
        if loopCount % 10 == 0 then
            print("[" .. os.date("%H:%M:%S") .. "] 心跳检测 #" .. loopCount)
            
            -- 测试一些核心函数
            if loopCount == 10 then
                print("测试聊天创建...")
                if dispatchTcp then
                    -- 模拟聊天创建请求 (假设包ID 1001是聊天创建)
                    local playerID, otString, retCode, retPacketID, retBufferLen, retString = 
                        dispatchTcp(1001, 12345, "test_channel", 1)
                    print("聊天创建测试结果: " .. (retCode or "nil"))
                end
            end
            
            if loopCount == 20 then
                print("测试心跳包...")
                if dispatchTcp then
                    -- 模拟心跳包 (假设包ID 1009是心跳)
                    local playerID, otString, retCode, retPacketID, retBufferLen, retString = 
                        dispatchTcp(1009, 12345, "", 1)
                    print("心跳测试结果: " .. (retCode or "nil"))
                end
            end
        end
        
        -- 模拟定时器处理
        if dispatchTimer then
            dispatchTimer(os.time(), "main")
        end
        
        -- 休眠1秒
        os.execute("ping 127.0.0.1 -n 2 >nul")
        
        -- 运行30次后退出演示
        if loopCount >= 30 then
            print("演示完成，服务器退出")
            break
        end
    end
end

-- 主函数
function main()
    print("Lua版本: " .. _VERSION)
    
    -- 创建目录
    createDirectories()
    
    -- 初始化服务器
    if not serverInit() then
        print("服务器初始化失败，退出")
        return 1
    end
    
    -- 启动网络服务
    startNetworkServer()
    
    -- 进入消息循环
    messageLoop()
    
    print("\n=== 服务器已停止 ===")
    return 0
end

-- 错误处理
function errorHandler(err)
    print("\n=== 发生错误 ===")
    print("错误信息: " .. (err or "未知错误"))
    print("堆栈跟踪:")
    print(debug.traceback())
    return err
end

-- 启动服务器
local success, result = xpcall(main, errorHandler)

if success then
    print("服务器正常退出，返回码: " .. (result or 0))
else
    print("服务器异常退出")
    os.exit(1)
end
