-- Lua替代C++函数实现
-- 用于在纯Lua环境下运行项目

print("加载Lua C++函数替代实现...")

-- 平台检测函数
function c_platfrom()
    return "luaself"
end

-- 日志函数
function c_clog(fileName, content)
    -- 确保日志目录存在
    local logDir = string.match(fileName, "(.*/)")
    if logDir then
        os.execute("mkdir \"" .. logDir .. "\" 2>nul")
    end
    
    -- 写入日志文件
    local file = io.open(fileName, "a")
    if file then
        file:write(content .. "\n")
        file:close()
        print("[LOG] " .. fileName .. ": " .. content)
    else
        print("[LOG ERROR] 无法写入: " .. fileName)
    end
end

-- 聊天日志函数
function c_chatlog(fileName, content)
    c_clog(fileName, content)
end

-- 加密函数 (简化实现)
function c_md5(str)
    -- 简化的MD5实现，实际项目中应使用真正的MD5库
    return "md5_" .. string.len(str) .. "_" .. string.sub(str, 1, 8)
end

function c_sha1(str)
    return "sha1_" .. string.len(str) .. "_" .. string.sub(str, 1, 10)
end

function c_sha256(str)
    return "sha256_" .. string.len(str) .. "_" .. string.sub(str, 1, 12)
end

function c_base64(str)
    -- 简化的base64编码
    return "base64_" .. str
end

-- HTTP函数 (模拟实现)
function c_httpPost(url, content)
    print("[HTTP POST] " .. url)
    print("[HTTP CONTENT] " .. (content or ""))
    return "HTTP_RESPONSE_OK"
end

function c_httpPostRpc(url, content)
    print("[HTTP POST RPC] " .. url)
    return "RPC_RESPONSE_OK"
end

function c_httpPostWithHead(url, content, head)
    print("[HTTP POST WITH HEAD] " .. url)
    print("[HTTP HEAD] " .. (head or ""))
    return "HTTP_HEAD_RESPONSE_OK"
end

function c_httpGet(url, content)
    print("[HTTP GET] " .. url)
    return "HTTP_GET_RESPONSE_OK"
end

function c_httpGetWithHead(url, content, head)
    print("[HTTP GET WITH HEAD] " .. url)
    return "HTTP_GET_HEAD_RESPONSE_OK"
end

-- 系统信息函数
function c_getServerInfo()
    -- 返回6个参数：controller最大线程数，controller空闲线程数，controller待处理事务总数，worker最大线程数，worker空闲线程数，worker待处理事务总数
    return 10, 8, 2, 20, 15, 5
end

function c_getSysInfo(processName)
    -- 返回4个参数：CPU总数，CPU使用率，内存总量，可使用内存总量
    print("[SYS INFO] 进程名: " .. (processName or "unknown"))
    return 8, 25.5, 16384, 12288
end

-- 任务调度函数
function c_dispatchGameJob(indexStr, dataStr)
    print("[DISPATCH JOB] " .. indexStr .. " -> " .. (dataStr or ""))
end

function c_setTimer(timeSec, indexStr, dataStr)
    print("[SET TIMER] " .. timeSec .. "s -> " .. indexStr)
end

function c_setLoopTimer(timeSec, indexStr, dataStr)
    print("[SET LOOP TIMER] " .. timeSec .. "s -> " .. indexStr)
end

-- 消息发送函数
function c_sendMessage(userIDStr, packetID, operatorID, buffLen, content)
    print("[SEND MESSAGE] 用户:" .. userIDStr .. " 包ID:" .. packetID .. " 操作ID:" .. operatorID)
    print("[MESSAGE CONTENT] " .. (content or ""))
end

-- 调试函数
function c_luaprint()
    print("[C++ LUA PRINT] 调用了C++的luaprint函数")
end

-- 打印函数
function luaPrint(msg)
    print("[LUA PRINT] " .. (msg or ""))
end

-- 数学函数扩展
function math.newrandomseed()
    math.randomseed(os.time())
end

-- 字符串扩展函数
function string.split(str, delimiter)
    local result = {}
    local pattern = "(.-)" .. delimiter
    local lastEnd = 1
    local s, e, cap = str:find(pattern, 1)
    while s do
        if s ~= 1 or cap ~= "" then
            table.insert(result, cap)
        end
        lastEnd = e + 1
        s, e, cap = str:find(pattern, lastEnd)
    end
    if lastEnd <= #str then
        cap = str:sub(lastEnd)
        table.insert(result, cap)
    end
    return result
end

function string.trim(str)
    return str:match("^%s*(.-)%s*$")
end

print("Lua C++函数替代实现加载完成!")
