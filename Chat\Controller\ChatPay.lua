module("ChatPay", package.seeall)

--心跳
function execute(packetID, operateID, buffer)

	local cgmsg = msg_chat_pb.cgchatpay()
	local gcmsg = msg_chat_pb.gcchatpay()
	
	cgmsg:ParseFromString(buffer)
	
	--检查该订单是否存在

	local sqlCase = "select id,status from dy_customer_order where id>0 and merchant_order_id='"..cgmsg.pcorderid.."'"
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch({})
	
	if sqlData == nil then
		gcmsg.result, gcmsg.msg = -1,"该订单不存在"
		return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if tonumber(sqlData[2]) ~= 1 then
		gcmsg.result, gcmsg.msg = -1,"订单状态错误"
		return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	ChatModel.PushPayToUpdate("merchant", sqlData[1])
	
	gcmsg.result = 0
	gcmsg.msg = "发送成功"
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end