// JavaScript模拟Lua逻辑测试
// 用于验证我们的Lua代码逻辑是否正确

console.log("=== Lua逻辑测试 (JavaScript模拟) ===");

// 模拟Lua的全局变量
let g_platfrom = 'luaself';
let logList = [];
let g_randomsee = 0;

// 模拟配置变量
let g_serverip = "127.0.0.1";
let g_tcpport = 6011;
let g_httpport = 6012;
let g_websocketport = 6013;

// 模拟Lua的os.date函数
function osDate(format, time) {
    const date = time ? new Date(time * 1000) : new Date();
    if (format === "%H:%M:%S") {
        return date.toTimeString().split(' ')[0];
    } else if (format === "%Y-%m-%d") {
        return date.toISOString().split('T')[0];
    }
    return date.toString();
}

// 模拟Lua的os.time函数
function osTime() {
    return Math.floor(Date.now() / 1000);
}

// 模拟C++函数
function c_clog(fileName, content) {
    console.log(`[LOG] ${fileName}: ${content}`);
}

function c_md5(str) {
    return `md5_${str.length}_${str.substring(0, 8)}`;
}

// 模拟LogFile函数
function LogFile(logLevel, content) {
    content = `[${osDate("%H:%M:%S", osTime())}]${content}`;
    if (g_platfrom !== 'luaself') {
        const fileName = osDate("%Y-%m-%d", osTime());
        const fullFileName = `./log/${fileName}-${logLevel}.log`;
        c_clog(fullFileName, content);
    } else {
        console.log(`[${logLevel.toUpperCase()}] ${content}`);
    }
}

// 模拟sendLog函数
function sendLog(msg) {
    let logdata = "sendlog:";
    if (g_platfrom !== 'luaself') {
        for (let i = 0; i < logList.length; i++) {
            logdata += '|' + logList[i];
        }
        logdata += "|" + msg;
    }
    LogFile("error", logdata);
    console.log(logdata);
}

// 模拟logRecoder函数
function logRecoder(msg) {
    if (logList.length >= 50) {
        logList.shift();
    }
    logList.push(msg);
}

// 模拟dispatchTcp函数
function dispatchTcp(packetId, operateId, buffer, threadId) {
    console.log(`\n=== 处理TCP请求 ===`);
    console.log(`包ID: ${packetId}, 操作ID: ${operateId}`);
    console.log(`缓冲区: ${buffer}, 线程ID: ${threadId}`);
    
    // 清空日志列表
    logList = [];
    
    let retCode = 0;
    let retPacketID = 0;
    let retBufferLen = 0;
    let retString = "";
    let otString = "";
    let playerID = 0;
    
    logRecoder(`packetId=${packetId}`);
    logRecoder(`operateId=${operateId}`);
    
    if (!packetId || packetId === 0) {
        LogFile("error", "packetID is nil");
        return [0, 'error', -1, 1, 5, 'error'];
    }
    
    // 模拟不同的包处理
    switch (packetId) {
        case 1001: // 聊天创建
            console.log("处理聊天创建请求");
            retCode = 200;
            retPacketID = 1001;
            retString = "聊天创建成功";
            otString = "chat_create";
            playerID = 12345;
            break;
            
        case 1009: // 心跳包
            console.log("处理心跳包");
            retCode = 200;
            retPacketID = 1009;
            retString = "心跳正常";
            otString = "heartbeat";
            playerID = 12345;
            break;
            
        default:
            console.log(`未知包类型: ${packetId}`);
            retCode = 404;
            retString = "未知请求";
            break;
    }
    
    console.log(`返回: 玩家ID=${playerID}, 代码=${retCode}, 字符串=${retString}`);
    return [playerID, otString, retCode, retPacketID, retBufferLen, retString];
}

// 测试主要功能
function testMainFunctions() {
    console.log("\n=== 测试主要功能 ===");
    
    // 测试日志功能
    console.log("\n1. 测试日志功能:");
    LogFile("info", "服务器启动");
    LogFile("warning", "这是一个警告");
    LogFile("error", "这是一个错误");
    
    // 测试加密功能
    console.log("\n2. 测试加密功能:");
    console.log("MD5测试:", c_md5("hello world"));
    
    // 测试TCP消息处理
    console.log("\n3. 测试TCP消息处理:");
    
    // 测试聊天创建
    let result1 = dispatchTcp(1001, 12345, "test_channel", 1);
    console.log("聊天创建结果:", result1);
    
    // 测试心跳包
    let result2 = dispatchTcp(1009, 12345, "", 1);
    console.log("心跳包结果:", result2);
    
    // 测试未知包
    let result3 = dispatchTcp(9999, 12345, "unknown", 1);
    console.log("未知包结果:", result3);
}

// 模拟服务器运行
function simulateServer() {
    console.log("\n=== 模拟服务器运行 ===");
    console.log(`平台: ${g_platfrom}`);
    console.log(`服务器配置:`);
    console.log(`  IP: ${g_serverip}`);
    console.log(`  TCP端口: ${g_tcpport}`);
    console.log(`  HTTP端口: ${g_httpport}`);
    console.log(`  WebSocket端口: ${g_websocketport}`);
    
    testMainFunctions();
    
    console.log("\n=== 服务器模拟完成 ===");
}

// 运行测试
try {
    simulateServer();
    console.log("\n✓ 所有测试通过！Lua逻辑验证成功。");
} catch (error) {
    console.error("\n✗ 测试失败:", error.message);
}
