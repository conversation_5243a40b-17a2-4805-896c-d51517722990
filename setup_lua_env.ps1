# 设置Lua环境的PowerShell脚本
param(
    [string]$LuaPath = ".\lua51"
)

Write-Host "=== 设置Lua 5.1环境 ===" -ForegroundColor Green

# 创建Lua目录
if (!(Test-Path $LuaPath)) {
    New-Item -ItemType Directory -Path $LuaPath -Force
    Write-Host "创建目录: $LuaPath" -ForegroundColor Yellow
}

# 检查是否已有lua.exe
if (Test-Path "$LuaPath\lua.exe") {
    Write-Host "发现已有的lua.exe" -ForegroundColor Green
    & "$LuaPath\lua.exe" -v
} else {
    Write-Host "需要下载Lua 5.1..." -ForegroundColor Yellow
    Write-Host "由于网络限制，请手动下载:" -ForegroundColor Red
    Write-Host "1. 访问: https://luabinaries.sourceforge.net/download.html" -ForegroundColor Cyan
    Write-Host "2. 下载: lua-5.1.5_Win64_bin.zip" -ForegroundColor Cyan
    Write-Host "3. 解压lua.exe到: $LuaPath\" -ForegroundColor Cyan
    Write-Host ""
    
    # 创建一个简单的Lua测试
    $testScript = @"
print("Lua 5.1 测试成功!")
print("版本: " .. _VERSION)
print("当前时间: " .. os.date())
"@
    $testScript | Out-File -FilePath "$LuaPath\test.lua" -Encoding UTF8
    Write-Host "创建了测试文件: $LuaPath\test.lua" -ForegroundColor Green
}

# 设置临时PATH
$env:PATH = "$((Get-Location).Path)\$LuaPath;$env:PATH"
Write-Host "临时添加到PATH: $LuaPath" -ForegroundColor Yellow

# 测试Lua是否可用
Write-Host "`n测试Lua环境..." -ForegroundColor Yellow
try {
    if (Test-Path "$LuaPath\lua.exe") {
        Write-Host "Lua可执行文件存在" -ForegroundColor Green
        $version = & "$LuaPath\lua.exe" -v 2>&1
        Write-Host "版本信息: $version" -ForegroundColor Cyan
    } else {
        Write-Host "Lua可执行文件不存在，需要手动下载" -ForegroundColor Red
    }
} catch {
    Write-Host "Lua测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 环境设置完成 ===" -ForegroundColor Green
