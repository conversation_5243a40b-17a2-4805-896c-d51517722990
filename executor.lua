
require("LoadHelper")
--test
g_platfrom = 'luaself'           --g_platfrom init for luaself
if type(c_platfrom) == 'function' then
	g_platfrom = c_platfrom()    --from C++ always is 'win32' or 'linux'
end

logList = {}
g_randomsee = 0
function math.myrandom(...)
	if g_randomsee == 0 then
		math.newrandomseed()
		g_randomsee = 1
	end
	return math.random(...)
end

function sendLog(msg)
	local logdata = "sendlog:"
	if g_platfrom ~= 'luaself' then
		for i = 1,#logList do
			logdata = logdata .. '|' .. logList[i];
		end
		logdata = logdata.."|"..msg
	end
	LogFile("error", logdata)
	luaPrint(logdata)
end

function LogFile(logLevel, content)
	content = "["..os.date("%H:%M:%S", os.time()).."]"..content
	if g_platfrom ~= 'luaself' then
		local fileName = os.date("%Y-%m-%d", os.time())
		fileName = "./log/"..fileName.."-"..logLevel..".log"
		c_clog(fileName, content)
	end
end

function ChatFile(fileLevel, content)
	content = "["..os.date("%H:%M:%S", os.time()).."]"..content
	if g_platfrom ~= 'luaself' then
		local fileName = os.date("%Y-%m-%d", os.time())
		fileName = "./chat/"..fileName.."_"..fileLevel..".log"
		c_clog(fileName, content)
	end
end

function DebugFile(fileLevel, content)
	if g_platfrom ~= 'luaself' then
		local fileName = os.date("%Y-%m-%d", os.time())
		fileName = "./log/"..fileName.."-"..fileLevel..".log"
		c_clog(fileName, content)
	end
end


function md5(md5Str)
	return c_md5(md5Str)
end

function Sha1(content)
  return c_sha1(content)
end

function base64_encode(content)
  return c_base64(content)
end


function ChatLog(fileName, content)
	fileName = "./chat/"..fileName..".log"
	c_chatlog(fileName, content)
end

function HttpPost(url,content)
	return c_httpPost(url,content)
end

function HttpPostRPC(url,content)
	return c_httpPostRpc(url,content)
end


function HttpPostWithHead(url,content,head)
	return c_httpPostWithHead(url,content,head)
end

function HttpGet(url,content)
	return c_httpGet(url,content)
end

function HttpGetWithHead(url,content,head)
	return c_httpGetWithHead(url,content, head)
end

function GetServerInfo()    --获取改游戏的参数
	--返回6个参数
	--controller最大线程数，controller空闲线程数，controller待处理事务总数，worker最大线程数，worker空闲线程数，worker待处理事务总数
	return c_getServerInfo()
end

function GetSysInfo()     --获取系统参数
	--返回4个参数：
	--CPU总数，CPU使用率，内存总量，可使用内存总量
	return c_getSysInfo(g_processName == nil and "game" or g_processName)
end

--入参是两个参数，第一个是索引，第一个是参数信息
function processWork(indexStr, dataStr)
	c_dispatchGameJob(indexStr, dataStr)
end

function processTimer(timeSec, indexStr, dataStr)
	c_setTimer(timeSec, indexStr, dataStr)
end

function processLoopTimer(timeSec, indexStr, dataStr)
	--该函数设置的定时器，是在主线程serverloop的线程中执行
	c_setLoopTimer(timeSec, indexStr, dataStr)
end

function Sha256(content)
	return c_sha256(content) 
end

function logRecoder(msg)
	if #logList >= 50 then
		table.remove(logList, 1)
	end
	table.insert(logList, msg)
end

function logClear()
	logList = {}
end

function SendMessage(useridList, packetID, buffLen, content)

	if useridList == nil or buffLen == 0  then
		return
	end

	local userIDStr = ""
	if type(useridList) == "table" then
		if table.getn(useridList) == 0 then
			return
		end
		
		for key,values in pairs(useridList) do
			
				if #userIDStr > 0 then
					userIDStr = userIDStr.."|"
				end
				userIDStr = userIDStr..values
		end
	else
		userIDStr = useridList
	end
	if userIDStr == "" then
		return nil
	end
	
	local operatorID = math.myrandom(1000,9999)
	c_sendMessage(userIDStr, packetID, operatorID, buffLen, content)
	
end

function __G__TRACKBACK__(msg)
	local traceback = string.split(debug.traceback("", 2), "\n")
	string.trim(traceback[3])
	
	local trace = debug.traceback()
	local logData = "LUA_ERROR:"..string.trim(traceback[3]).."\n"..tostring(msg).."\n".."trace msg:"..trace.."\n".."operate list:"
	
	return logData
end

mysqlItem = mysqlConnect.new(g_dbtype, g_dbUser, g_dbPassword, g_dbHost, g_dbPort, g_dbDatabase)
redisItem = redisConnect.new(g_redisInfo.host,g_redisInfo.pass,g_redisInfo.port)

--mysqlOtc = mysqlConnect.new(g_dbtype, g_db_otcUser, g_db_otcPassword, g_db_otcHost, g_db_otcPort, g_db_otcDatabase)


function dispatchTcp( packetId, operateId, buffer, threadId)
	--print("33333="..g_hgyxDefine.max_user)
	logClear()   -- first cleal logList
	local retCode = 0
	local retPacketID = 0
	local retBufferLen = 0
 	local retString
	local controller
	local otString
	local playerID
	logRecoder('packetId='..packetId)
	logRecoder('operateId='..packetId)
	--myPrint(packetId)
	if packetId == nil or packetId == 0 then
		LogFile("error","packetID is nil")
		return 0,'error',-1,1,5,'error'
	end

	if packetId ~= 1009 then
		local t = 6
	end
	local function tcpExecute()
		
		retPacketID,controller = tcpManager:createController(packetId)
		
		if controller == nil then
			LogFile("error","error controller is nil : packetId="..packetId)
		else
			--LoadHelper.Reload()
			playerID, retCode, retBufferLen, retString, otString = controller.execute(packetId, operateId, buffer)
		end
	end

	local status, msg = xpcall(tcpExecute, __G__TRACKBACK__)
	
	if not status then
		c_luaprint()
		sendLog(msg)
	end
	--返回多一个额外的参数
	--MjGuangDongWin.WinTest()
	--MajiangHuUtils.GuiTest()
	--MjGuangDongHuHelp.HuTest()
	otString = otString or "head"
	playerID = playerID or 0
	--print("playerID="..playerID.." otString="..otString.."   retCode="..retCode.."  retPacketID="..retPacketID.."  retBufferLen="..retBufferLen)
	RedisHelper.SetRedis(playerID, operateId, retString)
	return playerID,otString,retCode,retPacketID,retBufferLen,retString
end

function dispatchWebSocket( packetId, operateId, buffer, threadId)
	
	logClear()
	logRecoder('packetIndex='..packetId)
	logRecoder('operateIndex='..operateId)
	if packetId ~= 1027 then
		print("packetId ="..packetId..",operateId="..operateId)
	end
	local retCode = 0
	local retPacketID = 0
	local retBufferLen = 0
 	local retString
	local controller
	local otString
	local playerID	
	
	local retString = 'error'
	local retSize = 0
	local function webExecute()
		retPacketID,controller = tcpManager:createController(packetId)
		
		if controller == nil then
			LogFile("error","error controller is nil : packetId="..packetId)
		else

			playerID, retCode, retBufferLen, retString, otString = controller.execute(packetId, operateId, buffer)
		end
	end
	
	local status, msg = xpcall(webExecute, __G__TRACKBACK__)
	
	if not status then
		c_luaprint()
		sendLog(msg)
	end
	--返回多一个额外的参数
	--MjGuangDongWin.WinTest()
	--MajiangHuUtils.GuiTest()
	--MjGuangDongHuHelp.HuTest()
	otString = otString or "head"
	playerID = playerID or 0
	--print("playerID="..playerID.." otString="..otString.."   retCode="..retCode.."  retPacketID="..retPacketID.."  retBufferLen="..retBufferLen)
	RedisHelper.SetRedis(playerID, operateId, retString)
	return playerID,otString,retCode,retPacketID,retBufferLen,retString
end

function dispatchHttp(packetIndex, operateIndex, buffer)
	logClear()
	logRecoder('packetIndex='..packetIndex)
	logRecoder('operateIndex='..operateIndex)
	
	if packetIndex == nil then
		LogFile("error","packetIndex is nil")
		return 'error'
	end
	local retString = 'error'
	local retSize = 0
	local function httpExecute()
		local controller = httpManager:createController(packetIndex)
		
		if controller ~= nil then
			LoadHelper.HttpReload()
			retString,retSize = controller[operateIndex](buffer)
		end
	end
	
	local status, msg = xpcall(httpExecute, __G__TRACKBACK__)
	
	if not status then
		sendLog(msg)
	end
	
	
	return retString
end

function TcpSessionClose(playerID)
	if tonumber(playerID) == 0 then
		return
	end

	ChatModel.Logout(playerID)

end

function TcpSessionReport(userID, IP)
	--上报玩家的IP，目前主要是IP	
	--
	
end

function dispatchTimer( currTime, timeType)
	logClear()
	logRecoder('currTime='..currTime)
	--print(currTime .."--------------"..timeType)
	local function timerExecute()
		timerManager:execute(currTime, timeType)
	end
	
	local status, msg = xpcall(timerExecute, __G__TRACKBACK__)
	if not status then
		sendLog(msg)
	end
end

function dispatchTimerOnce( indexStr, dataStr)
	logClear()
	
	--print("indexStr================"..indexStr..",dataStr="..dataStr)
	local function timerExecute()
		local timer = timerManager:createOnceTimer(indexStr)
		
		if timer == nil then
			sendLog('error dispatchTimerOnce is nil : indexStr='..indexStr)
		else
			timer.execute(dataStr)
		end
	end
	
	local status, msg = xpcall(timerExecute, __G__TRACKBACK__)
	
	if not status then
		sendLog(msg)
	end
end


function timerInit(currTime, timeType)
	logRecoder('currTime='..currTime)
	
	local function timerInitData()
		timerManager.Init(currTime, timeType)
	end
	
	local status, msg = xpcall(timerInitData, __G__TRACKBACK__)
	if not status then
		sendLog(msg)
	end
end


function dispatchWorker(indexStr, dataStr)

	--传入的参数中，第一个是index索引，字符串类型的，第二个是参数
	local function workExecute()
		local worker = workManager:createWork(indexStr)
		
		if worker == nil then
			sendLog('error workerExexute is nil : indexStr='..indexStr)
		else
			worker.work(dataStr)
		end
	end
	
	local status, msg = xpcall(workExecute, __G__TRACKBACK__)
	
	if not status then
		sendLog(msg)
	end
	
end

math.newrandomseed()




