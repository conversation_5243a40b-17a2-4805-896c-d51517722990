@echo off
echo === Lua 5.1 安装脚本 ===
echo.

REM 检查是否已安装Lua
lua -v >nul 2>&1
if %errorlevel% == 0 (
    echo Lua已安装:
    lua -v
    echo.
    goto :test_project
)

echo Lua未安装，需要手动安装...
echo.
echo 请按照以下步骤安装Lua 5.1:
echo 1. 访问: https://luabinaries.sourceforge.net/download.html
echo 2. 下载: lua-5.1.5_Win64_bin.zip
echo 3. 解压到: C:\lua51\
echo 4. 将 C:\lua51 添加到PATH环境变量
echo 5. 重新运行此脚本
echo.
pause
goto :end

:test_project
echo 测试项目文件...
echo.

if exist "lioGame.exe" (
    echo [OK] lioGame.exe 存在
) else (
    echo [ERROR] lioGame.exe 不存在
)

if exist "executor.lua" (
    echo [OK] executor.lua 存在
) else (
    echo [ERROR] executor.lua 不存在
)

if exist "LoadHelper.lua" (
    echo [OK] LoadHelper.lua 存在
) else (
    echo [ERROR] LoadHelper.lua 不存在
)

if exist "conf\serverConf.lua" (
    echo [OK] conf\serverConf.lua 存在
) else (
    echo [ERROR] conf\serverConf.lua 不存在
)

echo.
echo 尝试运行Lua测试...
if exist "test_lua_env.lua" (
    lua test_lua_env.lua
) else (
    echo test_lua_env.lua 不存在，跳过测试
)

echo.
echo === 安装检查完成 ===
echo.
echo 下一步:
echo 1. 安装MySQL数据库
echo 2. 安装Redis缓存
echo 3. 运行: lioGame.exe utils
echo.

:end
pause
