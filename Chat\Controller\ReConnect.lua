module("ReConnect", package.seeall)

--重连
function execute(packetID, operateID, buffer)

	local cgmsg = msg_chat_pb.cgreconnect()
	local gcmsg = msg_chat_pb.gcreconnect()
	cgmsg:ParseFromString(buffer)
	

	local getRand = math.myrandom(100000000, 900000000)
	ChatModel.Login(cgmsg.pcorderid)   --重连也需要恢复
	gcmsg.systime = TimeUtils.GetTime()
	gcmsg.result = 0
	return cgmsg.pcorderid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
end