# 获取Lua 5.1的PowerShell脚本
Write-Host "=== 获取Lua 5.1环境 ===" -ForegroundColor Green

# 创建lua目录
$luaDir = ".\lua51"
if (!(Test-Path $luaDir)) {
    New-Item -ItemType Directory -Path $luaDir -Force
    Write-Host "创建目录: $luaDir" -ForegroundColor Yellow
}

# 尝试从多个源下载Lua
$urls = @(
    @{
        url = "https://sourceforge.net/projects/luabinaries/files/5.1.5/Tools%20Executables/lua-5.1.5_Win64_bin.zip/download"
        file = "lua-5.1.5_Win64_bin.zip"
        type = "zip"
    },
    @{
        url = "https://github.com/rjpcomputing/luaforwindows/releases/download/v5.1.5-52/LuaForWindows_v5.1.5-52.exe"
        file = "LuaForWindows.exe"
        type = "exe"
    }
)

$downloaded = $false

foreach ($item in $urls) {
    if ($downloaded) { break }
    
    try {
        Write-Host "尝试下载: $($item.url)" -ForegroundColor Yellow
        
        # 使用不同的下载方法
        $methods = @(
            { Invoke-WebRequest -Uri $item.url -OutFile $item.file -TimeoutSec 30 -UseBasicParsing },
            { (New-Object System.Net.WebClient).DownloadFile($item.url, $item.file) },
            { curl -L -o $item.file $item.url }
        )
        
        foreach ($method in $methods) {
            try {
                & $method
                if (Test-Path $item.file) {
                    Write-Host "下载成功: $($item.file)" -ForegroundColor Green
                    $downloaded = $true
                    break
                }
            } catch {
                Write-Host "方法失败，尝试下一个..." -ForegroundColor Gray
            }
        }
    } catch {
        Write-Host "下载失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

if (!$downloaded) {
    Write-Host "自动下载失败，创建手动下载指南..." -ForegroundColor Yellow
    
    $guide = @"
=== Lua 5.1 手动安装指南 ===

由于网络限制，请手动下载Lua 5.1:

方法1: 下载预编译版本
1. 访问: https://luabinaries.sourceforge.net/download.html
2. 下载: lua-5.1.5_Win64_bin.zip
3. 解压lua.exe到: $luaDir\

方法2: 下载完整安装包
1. 访问: https://github.com/rjpcomputing/luaforwindows/releases
2. 下载: LuaForWindows_v5.1.5-52.exe
3. 安装后复制lua.exe到: $luaDir\

方法3: 使用包管理器
# Chocolatey
choco install lua

# Scoop  
scoop install lua

完成后运行: .\test_lua_env.cmd
"@
    
    $guide | Out-File -FilePath "LUA_INSTALL_GUIDE.txt" -Encoding UTF8
    Write-Host "安装指南已保存到: LUA_INSTALL_GUIDE.txt" -ForegroundColor Green
}

# 检查是否有lua.exe
if (Test-Path "$luaDir\lua.exe") {
    Write-Host "发现Lua可执行文件!" -ForegroundColor Green
    & "$luaDir\lua.exe" -v
} else {
    Write-Host "Lua可执行文件不存在，请按照指南手动安装" -ForegroundColor Red
}

Write-Host "`n=== 完成 ===" -ForegroundColor Green
