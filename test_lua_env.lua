#!/usr/bin/env lua

-- 测试Lua环境和项目依赖
print("=== Lua环境测试 ===")
print("Lua版本: " .. _VERSION)

-- 测试基本的Lua功能
print("\n=== 基本功能测试 ===")
local test_table = {a = 1, b = 2, c = 3}
print("表操作测试: " .. test_table.a)

-- 测试数学库
print("数学库测试: " .. math.sin(math.pi/2))

-- 测试字符串库
print("字符串库测试: " .. string.upper("hello"))

-- 测试文件操作
print("\n=== 文件系统测试 ===")
local current_dir = io.popen("cd"):read("*l")
print("当前目录: " .. (current_dir or "无法获取"))

-- 测试项目文件是否存在
local function file_exists(name)
    local f = io.open(name, "r")
    if f ~= nil then
        io.close(f)
        return true
    else
        return false
    end
end

print("\n=== 项目文件检查 ===")
local project_files = {
    "LoadHelper.lua",
    "executor.lua",
    "conf/serverConf.lua",
    "conf/redisConf.lua",
    "trdlib/Init.lua",
    "Chat/Init.lua",
    "common/proto/msg_chat.proto"
}

for _, file in ipairs(project_files) do
    if file_exists(file) then
        print("✓ " .. file .. " 存在")
    else
        print("✗ " .. file .. " 不存在")
    end
end

-- 尝试加载项目的第三方库
print("\n=== 第三方库测试 ===")

-- 测试package.path
print("当前package.path:")
for path in string.gmatch(package.path, "[^;]+") do
    print("  " .. path)
end

-- 尝试加载项目依赖
local function safe_require(module_name)
    local success, result = pcall(require, module_name)
    if success then
        print("✓ " .. module_name .. " 加载成功")
        return result
    else
        print("✗ " .. module_name .. " 加载失败: " .. result)
        return nil
    end
end

-- 测试项目核心模块
print("\n=== 项目模块测试 ===")
safe_require("trdlib.Init")

print("\n=== 测试完成 ===")
