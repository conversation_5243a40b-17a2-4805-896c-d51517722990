module('timerManager', package.seeall)


function timerManager.Init(currTime, timerType)
		
	g_markTime.curr = TimeUtils.GetTableTime()
	
	if timerType == "gametimer" then

		
		
	elseif timerType == "utilstimer" then
		

		
	end
	
	g_markTime.last = g_markTime.curr
	
	print(timerType.." init end")
end


function timerManager:execute(currTime, timerType)
	
	if g_markTime.last == nil then
		g_markTime.last = TimeUtils.GetTableTime()
		print("================"..timerType)
		return
	end
	
	
	
	--每分钟更新一次服务器的数据	
	g_markTime.curr = TimeUtils.GetTableTime()
	

	ChatServices.ServerLoop()
	
	g_markTime.last = g_markTime.curr
	
end

function timerManager:createOnceTimer(strIndex)
	return _G[strIndex]
end


